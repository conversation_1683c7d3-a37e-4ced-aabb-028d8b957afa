# use new container-based infrastructure
sudo: false

language: python

matrix:
  include:
    - env: TOXENV=py27
      python: 2.7
    - env: TOXENV=py34
      python: 3.4
    - env: TOXENV=py35
      python: 3.5
    - env: TOXENV=py36
      python: 3.6
    - env: TOXENV=py37
      python: 3.7
      dist: xenial
      sudo: true
    - env: TOXENV=coverage
    - env: TOXENV=pep8

cache:
  pip: true
  directories:
    - .tox

install: pip install codecov tox

script:
  - tox

# publish coverage only after a successful build
after_success:
  - codecov
