# Tox (http://tox.testrun.org/) is a tool for running tests in
# multiple virtualenvs. This configuration file will run the
# test suite on all supported python versions. To use it, "pip
# install tox" and then run "tox" from this directory.

[tox]
envlist = py26, py27, py32, py33, py34, py35, py36, py37, pypy, coverage, pep8

[testenv]
commands = py.test
deps = pytest
       mock


[testenv:py27]
commands = py.test --doctest-glob=README.rst  # test documentation
deps = pytest
       mock

[testenv:pep8]
# pep8 disabled for
# - E701 (multiple statements on one line)
# - E126 (continuation line over-indented for hanging indent)
# - W504 (line break after binary operator)
commands = flake8 --max-line-length=90 --show-source -v --count --ignore=E701,E126,W504
deps = flake8

[testenv:coverage]
basepython=python2
commands = coverage erase
           py.test --doctest-glob=README.rst --cov schema
           coverage report -m
deps = pytest
       pytest-cov
       coverage
       mock

[flake8]
exclude=.venv,.git,.tox
